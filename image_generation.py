"""
Image Generation System using Stable Diffusion
Optimized for RTX 2070 with 8GB VRAM
"""
import asyncio
import logging
import torch
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import json
from PIL import Image
import numpy as np

# Diffusers imports
from diffusers import (
    StableDiffusionPipeline, 
    StableDiffusionImg2ImgPipeline,
    DPMSolverMultistepScheduler,
    EulerAncestralDiscreteScheduler
)
from diffusers.utils import load_image

from config import config
from system_monitor import resource_monitor

logger = logging.getLogger(__name__)

class ImageGenerator:
    """Image generation system using Stable Diffusion"""
    
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.txt2img_pipeline = None
        self.img2img_pipeline = None
        self.current_model = config.STABLE_DIFFUSION_MODEL
        self.initialized = False
        
        # Generation settings optimized for RTX 2070
        self.default_settings = {
            "width": config.IMAGE_WIDTH,
            "height": config.IMAGE_HEIGHT,
            "num_inference_steps": config.INFERENCE_STEPS,
            "guidance_scale": 7.5,
            "negative_prompt": "blurry, low quality, distorted, deformed, ugly, bad anatomy",
            "num_images_per_prompt": 1
        }
        
        # Style presets for different character types
        self.style_presets = {
            "realistic_portrait": {
                "positive_suffix": ", photorealistic, detailed face, high quality, professional photography",
                "negative_suffix": ", cartoon, anime, painting, sketch, low quality, blurry"
            },
            "anime_style": {
                "positive_suffix": ", anime style, detailed anime art, high quality anime",
                "negative_suffix": ", realistic, photographic, 3d render, low quality"
            },
            "fantasy_art": {
                "positive_suffix": ", fantasy art, detailed digital painting, epic fantasy style",
                "negative_suffix": ", modern, contemporary, low quality, blurry"
            },
            "cyberpunk": {
                "positive_suffix": ", cyberpunk style, neon lights, futuristic, detailed digital art",
                "negative_suffix": ", medieval, fantasy, low quality, blurry"
            }
        }
    
    async def initialize(self):
        """Initialize Stable Diffusion pipelines"""
        try:
            logger.info("Initializing image generation system...")
            
            # Check VRAM availability
            resources = resource_monitor.get_current_resources()
            if resources.gpu_memory_total < 4000:  # Less than 4GB VRAM
                logger.warning("Low VRAM detected, using CPU mode")
                self.device = "cpu"
            
            await self._load_pipelines()
            
            self.initialized = True
            logger.info("Image generation system initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize image generation: {e}")
            raise
    
    async def _load_pipelines(self):
        """Load Stable Diffusion pipelines"""
        try:
            logger.info(f"Loading Stable Diffusion model: {self.current_model}")
            
            # Load text-to-image pipeline
            self.txt2img_pipeline = StableDiffusionPipeline.from_pretrained(
                self.current_model,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                safety_checker=None,  # Disable safety checker for uncensored content
                requires_safety_checker=False,
                cache_dir=str(config.MODELS_DIR / "diffusers")
            )
            
            # Optimize for RTX 2070
            if self.device == "cuda":
                self.txt2img_pipeline = self.txt2img_pipeline.to(self.device)
                # Try to enable memory optimizations if available
                try:
                    self.txt2img_pipeline.enable_xformers_memory_efficient_attention()
                except Exception as e:
                    logger.warning(f"Could not enable xformers attention: {e}")
                    # Fallback to model CPU offloading for memory efficiency
                    try:
                        self.txt2img_pipeline.enable_model_cpu_offload()
                    except Exception as e2:
                        logger.warning(f"Could not enable CPU offload: {e2}")
                
                # Use faster scheduler
                self.txt2img_pipeline.scheduler = DPMSolverMultistepScheduler.from_config(
                    self.txt2img_pipeline.scheduler.config
                )
            
            # Load img2img pipeline (shares components with txt2img)
            self.img2img_pipeline = StableDiffusionImg2ImgPipeline(
                vae=self.txt2img_pipeline.vae,
                text_encoder=self.txt2img_pipeline.text_encoder,
                tokenizer=self.txt2img_pipeline.tokenizer,
                unet=self.txt2img_pipeline.unet,
                scheduler=self.txt2img_pipeline.scheduler,
                safety_checker=None,
                requires_safety_checker=False,
                feature_extractor=self.txt2img_pipeline.feature_extractor
            )
            
            if self.device == "cuda":
                self.img2img_pipeline = self.img2img_pipeline.to(self.device)
            
            logger.info("Stable Diffusion pipelines loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading pipelines: {e}")
            raise
    
    async def generate_image(self, prompt: str, style: str = "realistic_portrait",
                           negative_prompt: Optional[str] = None,
                           **kwargs) -> str:
        """Generate image from text prompt"""
        if not self.initialized:
            await self.initialize()
        
        try:
            # Check if we can run this task
            if not resource_monitor.can_run_ai_task("image_generation", estimated_vram=4000):
                task_id = f"img_gen_{datetime.now().timestamp()}"
                await resource_monitor.queue_ai_task(
                    task_id, "image_generation",
                    lambda: self._generate_image_internal(prompt, style, negative_prompt, **kwargs),
                    estimated_vram=4000, priority=1
                )
                return "Generating image, please wait..."
            
            return await self._generate_image_internal(prompt, style, negative_prompt, **kwargs)
            
        except Exception as e:
            logger.error(f"Error generating image: {e}")
            return f"Error generating image: {str(e)}"
    
    async def _generate_image_internal(self, prompt: str, style: str,
                                     negative_prompt: Optional[str], **kwargs) -> str:
        """Internal method for image generation"""
        try:
            # Apply style preset
            styled_prompt = self._apply_style_preset(prompt, style)
            
            # Prepare negative prompt
            if not negative_prompt:
                negative_prompt = self.default_settings["negative_prompt"]
            
            if style in self.style_presets:
                negative_prompt += self.style_presets[style]["negative_suffix"]
            
            # Merge settings
            generation_settings = {**self.default_settings, **kwargs}
            generation_settings["prompt"] = styled_prompt
            generation_settings["negative_prompt"] = negative_prompt
            
            # Generate image in thread pool
            loop = asyncio.get_event_loop()
            image = await loop.run_in_executor(
                None,
                self._run_generation,
                generation_settings
            )
            
            # Save image
            output_path = config.MEDIA_DIR / f"generated_{datetime.now().timestamp()}.png"
            image.save(output_path)
            
            logger.info(f"Generated image: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Internal generation error: {e}")
            raise
    
    def _run_generation(self, settings: Dict[str, Any]) -> Image.Image:
        """Run image generation (blocking operation)"""
        try:
            with torch.autocast(self.device):
                result = self.txt2img_pipeline(**settings)
                return result.images[0]
                
        except Exception as e:
            logger.error(f"Generation execution error: {e}")
            raise
    
    def _apply_style_preset(self, prompt: str, style: str) -> str:
        """Apply style preset to prompt"""
        if style in self.style_presets:
            return prompt + self.style_presets[style]["positive_suffix"]
        return prompt
    
    async def generate_character_avatar(self, character_name: str, description: str,
                                      style: str = "realistic_portrait") -> str:
        """Generate avatar for AI character"""
        try:
            # Build character-specific prompt
            avatar_prompt = f"portrait of {character_name}, {description}"
            
            # Add uncensored generation capability
            if config.ENABLE_NSFW:
                avatar_prompt += ", detailed, high quality"
            
            return await self.generate_image(
                prompt=avatar_prompt,
                style=style,
                guidance_scale=8.0,  # Higher guidance for character consistency
                num_inference_steps=25  # More steps for better quality
            )
            
        except Exception as e:
            logger.error(f"Error generating character avatar: {e}")
            return ""
    
    async def modify_image(self, image_path: str, prompt: str, strength: float = 0.7,
                          style: str = "realistic_portrait") -> str:
        """Modify existing image using img2img"""
        if not self.initialized:
            await self.initialize()
        
        try:
            # Check resources
            if not resource_monitor.can_run_ai_task("image_modification", estimated_vram=4000):
                return "Image modification queued, please wait..."
            
            # Load input image
            input_image = load_image(image_path)
            
            # Apply style preset
            styled_prompt = self._apply_style_preset(prompt, style)
            
            # Generate modified image
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self._run_img2img,
                input_image, styled_prompt, strength
            )
            
            # Save result
            output_path = config.MEDIA_DIR / f"modified_{datetime.now().timestamp()}.png"
            result.save(output_path)
            
            logger.info(f"Modified image: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error modifying image: {e}")
            return ""
    
    def _run_img2img(self, image: Image.Image, prompt: str, strength: float) -> Image.Image:
        """Run img2img generation"""
        try:
            with torch.autocast(self.device):
                result = self.img2img_pipeline(
                    prompt=prompt,
                    image=image,
                    strength=strength,
                    guidance_scale=7.5,
                    num_inference_steps=20
                )
                return result.images[0]
                
        except Exception as e:
            logger.error(f"Img2img execution error: {e}")
            raise
    
    async def generate_scene(self, scene_description: str, characters: List[str] = None,
                           style: str = "fantasy_art") -> str:
        """Generate scene with multiple characters"""
        try:
            scene_prompt = f"scene: {scene_description}"
            
            if characters:
                character_list = ", ".join(characters)
                scene_prompt += f", featuring {character_list}"
            
            return await self.generate_image(
                prompt=scene_prompt,
                style=style,
                width=768,  # Wider for scenes
                height=512,
                guidance_scale=8.0,
                num_inference_steps=30
            )
            
        except Exception as e:
            logger.error(f"Error generating scene: {e}")
            return ""
    
    def get_available_styles(self) -> List[str]:
        """Get list of available style presets"""
        return list(self.style_presets.keys())
    
    def add_style_preset(self, name: str, positive_suffix: str, negative_suffix: str):
        """Add custom style preset"""
        self.style_presets[name] = {
            "positive_suffix": positive_suffix,
            "negative_suffix": negative_suffix
        }
        logger.info(f"Added style preset: {name}")
    
    async def change_model(self, model_name: str) -> bool:
        """Change the Stable Diffusion model"""
        try:
            logger.info(f"Changing model to: {model_name}")
            
            # Clear current pipelines
            if self.txt2img_pipeline:
                del self.txt2img_pipeline
            if self.img2img_pipeline:
                del self.img2img_pipeline
            
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
            
            # Load new model
            self.current_model = model_name
            await self._load_pipelines()
            
            logger.info(f"Successfully changed to model: {model_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error changing model: {e}")
            return False
    
    def get_generation_settings(self) -> Dict[str, Any]:
        """Get current generation settings"""
        return {
            "model": self.current_model,
            "device": self.device,
            "default_settings": self.default_settings,
            "available_styles": list(self.style_presets.keys())
        }
    
    def cleanup(self):
        """Clean up image generation resources"""
        try:
            if self.txt2img_pipeline:
                del self.txt2img_pipeline
            if self.img2img_pipeline:
                del self.img2img_pipeline
            
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
            
            logger.info("Image generation cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during image generation cleanup: {e}")

# Global image generator instance
image_generator = ImageGenerator()

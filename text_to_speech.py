"""
Text-to-Speech system using Coqui TTS
Fast, high-quality voice synthesis with multiple voice profiles
"""
import asyncio
import logging
import tempfile
import threading
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import torch
import numpy as np
import soundfile as sf
from datetime import datetime
import json

# TTS imports - handle missing dependencies gracefully
try:
    from TTS.api import TTS
    from TTS.tts.configs.xtts_config import XttsConfig
    from TTS.tts.models.xtts import Xtts
    TTS_AVAILABLE = True
except ImportError as e:
    print(f"Warning: TTS library not fully available: {e}")
    print("TTS functionality will be disabled. Install missing dependencies to enable TTS.")
    TTS_AVAILABLE = False
    TTS = None
    XttsConfig = None
    Xtts = None

from config import config
from system_monitor import resource_monitor

logger = logging.getLogger(__name__)

class VoiceProfile:
    """Voice profile for character voices"""
    def __init__(self, name: str, model_name: str, speaker: Optional[str] = None,
                 language: str = "en", speed: float = 1.0, pitch: float = 1.0):
        self.name = name
        self.model_name = model_name
        self.speaker = speaker
        self.language = language
        self.speed = speed
        self.pitch = pitch
        self.reference_audio = None  # For voice cloning

class TextToSpeech:
    """Text-to-Speech system with multiple voice profiles"""
    
    def __init__(self):
        if not TTS_AVAILABLE:
            self.device = "cpu"
            self.models = {}
            self.voice_profiles = {}
            self.current_model = None
            self.sample_rate = 22050
            self.initialized = False
            logger.warning("TTS not available - TTS functionality disabled")
            return

        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.models: Dict[str, TTS] = {}
        self.voice_profiles: Dict[str, VoiceProfile] = {}
        self.current_model = None
        self.sample_rate = config.TTS_SAMPLE_RATE
        self.initialized = False
        
        # Audio playback
        self.audio_queue = asyncio.Queue()
        self.playing = False
        
    async def initialize(self):
        """Initialize TTS system with optimized models"""
        try:
            logger.info("Initializing TTS system...")
            
            # Create voice profiles optimized for your RTX 2070
            await self._create_default_voice_profiles()
            
            # Load the primary model
            await self._load_primary_model()
            
            self.initialized = True
            logger.info("TTS system initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize TTS: {e}")
            raise
    
    async def _create_default_voice_profiles(self):
        """Create default voice profiles"""
        # Fast, lightweight models for RTX 2070
        profiles = [
            VoiceProfile("female_1", "tts_models/en/ljspeech/tacotron2-DDC", 
                        language="en", speed=1.0, pitch=1.0),
            VoiceProfile("male_1", "tts_models/en/ljspeech/tacotron2-DDC", 
                        language="en", speed=0.9, pitch=0.8),
            VoiceProfile("female_2", "tts_models/en/ljspeech/glow-tts", 
                        language="en", speed=1.1, pitch=1.2),
            VoiceProfile("male_2", "tts_models/en/ljspeech/glow-tts", 
                        language="en", speed=0.8, pitch=0.7),
        ]
        
        # Add multi-speaker model if VRAM allows
        if resource_monitor.get_current_resources().gpu_memory_total > 6000:
            profiles.extend([
                VoiceProfile("female_expressive", "tts_models/en/vctk/vits", 
                           speaker="p225", language="en"),
                VoiceProfile("male_expressive", "tts_models/en/vctk/vits", 
                           speaker="p226", language="en"),
            ])
        
        for profile in profiles:
            self.voice_profiles[profile.name] = profile
        
        logger.info(f"Created {len(profiles)} voice profiles")
    
    async def _load_primary_model(self):
        """Load the primary TTS model"""
        try:
            # Start with a lightweight model
            primary_profile = self.voice_profiles["female_1"]
            
            logger.info(f"Loading primary TTS model: {primary_profile.model_name}")
            
            # Load model with GPU optimization
            tts = TTS(
                model_name=primary_profile.model_name,
                progress_bar=False,
                gpu=torch.cuda.is_available()
            )
            
            self.models[primary_profile.model_name] = tts
            self.current_model = tts
            
            logger.info("Primary TTS model loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading primary model: {e}")
            # Fallback to CPU-only model
            try:
                tts = TTS(model_name="tts_models/en/ljspeech/tacotron2-DDC", gpu=False)
                self.models["fallback"] = tts
                self.current_model = tts
                logger.info("Loaded fallback TTS model")
            except Exception as fallback_error:
                logger.error(f"Fallback model also failed: {fallback_error}")
                raise
    
    async def synthesize_speech(self, text: str, voice_profile: str = "female_1",
                              output_path: Optional[str] = None) -> str:
        """Synthesize speech from text"""
        if not TTS_AVAILABLE:
            logger.warning("TTS not available - returning empty audio file path")
            # Create a dummy audio file path
            if output_path is None:
                output_path = f"temp_audio_{int(time.time())}.wav"
            return output_path

        if not self.initialized:
            await self.initialize()
        
        try:
            # Check if we can run this task
            if not resource_monitor.can_run_ai_task("tts_synthesis", estimated_vram=1500):
                task_id = f"tts_{datetime.now().timestamp()}"
                await resource_monitor.queue_ai_task(
                    task_id, "tts_synthesis",
                    lambda: self._synthesize_speech_internal(text, voice_profile, output_path),
                    estimated_vram=1500, priority=2
                )
                return "Generating speech, please wait..."
            
            return await self._synthesize_speech_internal(text, voice_profile, output_path)
            
        except Exception as e:
            logger.error(f"Error synthesizing speech: {e}")
            return f"Error generating speech: {str(e)}"
    
    async def _synthesize_speech_internal(self, text: str, voice_profile: str,
                                        output_path: Optional[str]) -> str:
        """Internal method for speech synthesis"""
        try:
            profile = self.voice_profiles.get(voice_profile)
            if not profile:
                logger.warning(f"Voice profile {voice_profile} not found, using default")
                profile = list(self.voice_profiles.values())[0]
            
            # Get or load the required model
            model = await self._get_model_for_profile(profile)
            
            # Create output path if not provided
            if not output_path:
                output_path = str(config.MEDIA_DIR / f"tts_{datetime.now().timestamp()}.wav")
            
            # Run synthesis in thread pool
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                self._run_synthesis,
                model, text, profile, output_path
            )
            
            logger.info(f"Generated speech: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Internal synthesis error: {e}")
            raise
    
    def _run_synthesis(self, model: TTS, text: str, profile: VoiceProfile, output_path: str):
        """Run TTS synthesis (blocking operation)"""
        try:
            # Prepare synthesis parameters
            kwargs = {
                "text": text,
                "file_path": output_path
            }
            
            # Add speaker if specified
            if profile.speaker and hasattr(model, 'speakers') and model.speakers:
                if profile.speaker in model.speakers:
                    kwargs["speaker"] = profile.speaker
            
            # Add language if supported
            if hasattr(model, 'languages') and model.languages:
                if profile.language in model.languages:
                    kwargs["language"] = profile.language
            
            # Synthesize
            model.tts_to_file(**kwargs)
            
            # Apply speed and pitch modifications if needed
            if profile.speed != 1.0 or profile.pitch != 1.0:
                self._modify_audio_properties(output_path, profile.speed, profile.pitch)
            
        except Exception as e:
            logger.error(f"Synthesis execution error: {e}")
            raise
    
    def _modify_audio_properties(self, audio_path: str, speed: float, pitch: float):
        """Modify audio speed and pitch"""
        try:
            import librosa
            import soundfile as sf
            
            # Load audio
            y, sr = librosa.load(audio_path, sr=None)
            
            # Modify speed (time stretching)
            if speed != 1.0:
                y = librosa.effects.time_stretch(y, rate=speed)
            
            # Modify pitch
            if pitch != 1.0:
                n_steps = 12 * np.log2(pitch)  # Convert to semitones
                y = librosa.effects.pitch_shift(y, sr=sr, n_steps=n_steps)
            
            # Save modified audio
            sf.write(audio_path, y, sr)
            
        except Exception as e:
            logger.warning(f"Could not modify audio properties: {e}")
    
    async def _get_model_for_profile(self, profile: VoiceProfile) -> TTS:
        """Get or load model for voice profile"""
        if profile.model_name in self.models:
            return self.models[profile.model_name]
        
        # Load new model
        try:
            logger.info(f"Loading model: {profile.model_name}")
            tts = TTS(
                model_name=profile.model_name,
                progress_bar=False,
                gpu=torch.cuda.is_available()
            )
            self.models[profile.model_name] = tts
            return tts
            
        except Exception as e:
            logger.error(f"Error loading model {profile.model_name}: {e}")
            # Return current model as fallback
            return self.current_model
    
    async def create_voice_profile(self, name: str, model_name: str, 
                                 speaker: Optional[str] = None,
                                 reference_audio: Optional[str] = None,
                                 **kwargs) -> bool:
        """Create a new voice profile"""
        try:
            profile = VoiceProfile(
                name=name,
                model_name=model_name,
                speaker=speaker,
                **kwargs
            )
            
            if reference_audio:
                profile.reference_audio = reference_audio
            
            self.voice_profiles[name] = profile
            
            # Save profile to file
            await self._save_voice_profile(profile)
            
            logger.info(f"Created voice profile: {name}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating voice profile: {e}")
            return False
    
    async def _save_voice_profile(self, profile: VoiceProfile):
        """Save voice profile to file"""
        try:
            profile_data = {
                "name": profile.name,
                "model_name": profile.model_name,
                "speaker": profile.speaker,
                "language": profile.language,
                "speed": profile.speed,
                "pitch": profile.pitch,
                "reference_audio": profile.reference_audio
            }
            
            profile_file = config.DATA_DIR / "voice_profiles" / f"{profile.name}.json"
            profile_file.parent.mkdir(exist_ok=True)
            
            with open(profile_file, 'w') as f:
                json.dump(profile_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving voice profile: {e}")
    
    async def load_voice_profiles(self):
        """Load voice profiles from files"""
        try:
            profiles_dir = config.DATA_DIR / "voice_profiles"
            if not profiles_dir.exists():
                return
            
            for profile_file in profiles_dir.glob("*.json"):
                try:
                    with open(profile_file, 'r') as f:
                        profile_data = json.load(f)
                    
                    profile = VoiceProfile(**profile_data)
                    self.voice_profiles[profile.name] = profile
                    
                except Exception as e:
                    logger.error(f"Error loading profile {profile_file}: {e}")
                    
        except Exception as e:
            logger.error(f"Error loading voice profiles: {e}")
    
    def get_available_voices(self) -> List[Dict[str, Any]]:
        """Get list of available voice profiles"""
        voices = []
        for name, profile in self.voice_profiles.items():
            voices.append({
                "name": name,
                "model": profile.model_name,
                "speaker": profile.speaker,
                "language": profile.language,
                "speed": profile.speed,
                "pitch": profile.pitch
            })
        return voices
    
    def get_available_models(self) -> List[str]:
        """Get list of available TTS models"""
        try:
            return TTS.list_models()
        except Exception as e:
            logger.error(f"Error getting available models: {e}")
            return []
    
    async def clone_voice(self, reference_audio_path: str, voice_name: str) -> bool:
        """Clone a voice from reference audio (if supported)"""
        try:
            # This would require XTTS or similar voice cloning model
            # For now, create a profile that references the audio
            profile = VoiceProfile(
                name=voice_name,
                model_name="tts_models/multilingual/multi-dataset/xtts_v2",
                reference_audio=reference_audio_path
            )
            
            self.voice_profiles[voice_name] = profile
            await self._save_voice_profile(profile)
            
            logger.info(f"Created voice clone profile: {voice_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error cloning voice: {e}")
            return False
    
    def cleanup(self):
        """Clean up TTS resources"""
        try:
            # Clear models from GPU memory
            for model in self.models.values():
                if hasattr(model, 'synthesizer') and hasattr(model.synthesizer, 'tts_model'):
                    if hasattr(model.synthesizer.tts_model, 'cuda'):
                        model.synthesizer.tts_model.cpu()
            
            self.models.clear()
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
            
            logger.info("TTS cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during TTS cleanup: {e}")

# Global TTS instance
tts_system = TextToSpeech()

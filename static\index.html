<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Character Chat System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
            height: 70vh;
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .chat-area {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }

        .section-title {
            font-size: 1.3em;
            margin-bottom: 15px;
            color: #4a5568;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 5px;
        }

        .character-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .character-item {
            padding: 10px;
            margin-bottom: 10px;
            background: #f7fafc;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .character-item:hover {
            background: #edf2f7;
            transform: translateY(-2px);
        }

        .character-item.active {
            background: #667eea;
            color: white;
            border-color: #5a67d8;
        }

        .character-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .character-desc {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 18px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .message.user {
            background: #667eea;
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .message.character {
            background: white;
            border: 1px solid #e2e8f0;
            margin-right: auto;
        }

        .message-input {
            display: flex;
            gap: 10px;
        }

        .message-input input {
            flex: 1;
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 25px;
            font-size: 1em;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .message-input input:focus {
            border-color: #667eea;
        }

        .btn {
            padding: 12px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }

        .btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
        }

        .controls {
            margin-bottom: 20px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .control-group input, .control-group select, .control-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 0.9em;
        }

        .control-group textarea {
            resize: vertical;
            min-height: 60px;
        }

        .status-bar {
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 20px;
            border-radius: 10px;
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9em;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #48bb78;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI Character Chat System</h1>
            <p>Uncensored AI characters with multimedia support</p>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <div class="section-title">Characters</div>
                <div class="character-list" id="characterList">
                    <!-- Characters will be loaded here -->
                </div>
                
                <div class="controls">
                    <button class="btn" onclick="showCreateCharacter()">Create New Character</button>
                </div>

                <div class="section-title">Settings</div>
                <div class="controls">
                    <div class="control-group">
                        <label>Voice Response</label>
                        <input type="checkbox" id="voiceResponse"> Enable TTS
                    </div>
                    <div class="control-group">
                        <label>Voice Profile</label>
                        <select id="voiceProfile">
                            <option value="female_1">Female 1</option>
                            <option value="male_1">Male 1</option>
                            <option value="female_2">Female 2</option>
                            <option value="male_2">Male 2</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="chat-area">
                <div class="section-title" id="chatTitle">Select a character to start chatting</div>
                <div class="chat-messages" id="chatMessages">
                    <div style="text-align: center; color: #a0aec0; margin-top: 50px;">
                        Choose a character from the sidebar to begin your conversation
                    </div>
                </div>
                <div class="message-input">
                    <input type="text" id="messageInput" placeholder="Type your message..." disabled>
                    <button class="btn" id="sendBtn" onclick="sendMessage()" disabled>Send</button>
                    <button class="btn" id="voiceBtn" onclick="toggleVoiceInput()" disabled>🎤</button>
                </div>
            </div>
        </div>

        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>System Online</span>
            </div>
            <div id="systemStatus">Ready</div>
        </div>
    </div>

    <!-- Create Character Modal (simplified) -->
    <div id="createCharacterModal" class="hidden" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;" onclick="closeModalOnBackdrop(event)">
        <div style="background: white; padding: 30px; border-radius: 15px; max-width: 500px; width: 90%;" onclick="event.stopPropagation()">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 style="margin: 0;">Create New Character</h3>
                <button onclick="hideCreateCharacter()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #666;">&times;</button>
            </div>
            <div class="control-group">
                <label>Name</label>
                <input type="text" id="newCharName" placeholder="Character name">
            </div>
            <div class="control-group">
                <label>Description</label>
                <textarea id="newCharDesc" placeholder="Describe your character..."></textarea>
            </div>
            <div class="control-group">
                <label>Personality Traits (comma-separated)</label>
                <input type="text" id="newCharTraits" placeholder="friendly, intelligent, humorous">
            </div>
            <div style="display: flex; gap: 10px; margin-top: 20px;">
                <button class="btn" onclick="createCharacter()">Create</button>
                <button class="btn" onclick="hideCreateCharacter()" style="background: #a0aec0;">Cancel</button>
            </div>
        </div>
    </div>

    <script>
        let currentCharacter = null;
        let currentConversation = null;
        let websocket = null;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadCharacters();
            setupEventListeners();
        });

        function setupEventListeners() {
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
        }

        async function loadCharacters() {
            try {
                const response = await fetch('/characters');
                const characters = await response.json();
                
                const characterList = document.getElementById('characterList');
                characterList.innerHTML = '';
                
                characters.forEach(character => {
                    const item = document.createElement('div');
                    item.className = 'character-item';
                    item.onclick = () => selectCharacter(character);
                    item.innerHTML = `
                        <div class="character-name">${character.name}</div>
                        <div class="character-desc">${character.description}</div>
                    `;
                    characterList.appendChild(item);
                });
                
                if (characters.length === 0) {
                    characterList.innerHTML = '<div style="text-align: center; color: #a0aec0;">No characters yet. Create one to get started!</div>';
                }
            } catch (error) {
                console.error('Error loading characters:', error);
            }
        }

        async function selectCharacter(character) {
            currentCharacter = character;
            
            // Update UI
            document.querySelectorAll('.character-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.character-item').classList.add('active');
            
            document.getElementById('chatTitle').textContent = `Chat with ${character.name}`;
            document.getElementById('chatMessages').innerHTML = '';
            document.getElementById('messageInput').disabled = false;
            document.getElementById('sendBtn').disabled = false;
            document.getElementById('voiceBtn').disabled = false;
            
            // Start conversation
            try {
                const response = await fetch('/conversations/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ character_id: character.id })
                });
                const data = await response.json();
                currentConversation = data.conversation_id;
                
                addMessage('system', `Started conversation with ${character.name}`);
            } catch (error) {
                console.error('Error starting conversation:', error);
            }
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || !currentConversation) return;
            
            // Add user message to chat
            addMessage('user', message);
            input.value = '';
            
            // Show loading
            const loadingId = addMessage('character', '<div class="loading"></div>');
            
            try {
                const response = await fetch('/conversations/message', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: message,
                        conversation_id: currentConversation,
                        voice_response: document.getElementById('voiceResponse').checked
                    })
                });
                
                const data = await response.json();
                
                // Remove loading and add response
                document.getElementById(loadingId).remove();
                addMessage('character', data.response);
                
                // Play voice if available
                if (data.voice_file) {
                    playAudio(data.voice_file);
                }
                
            } catch (error) {
                document.getElementById(loadingId).remove();
                addMessage('system', 'Error: Could not get response');
                console.error('Error sending message:', error);
            }
        }

        function addMessage(type, content) {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            const messageId = 'msg_' + Date.now();
            messageDiv.id = messageId;
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = content;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
            return messageId;
        }

        function showCreateCharacter() {
            document.getElementById('createCharacterModal').classList.remove('hidden');
        }

        function hideCreateCharacter() {
            document.getElementById('createCharacterModal').classList.add('hidden');
            // Clear form when hiding
            document.getElementById('newCharName').value = '';
            document.getElementById('newCharDesc').value = '';
            document.getElementById('newCharTraits').value = '';
        }

        function closeModalOnBackdrop(event) {
            if (event.target === event.currentTarget) {
                hideCreateCharacter();
            }
        }

        async function createCharacter() {
            const name = document.getElementById('newCharName').value.trim();
            const description = document.getElementById('newCharDesc').value.trim();
            const traits = document.getElementById('newCharTraits').value.trim();

            console.log('Creating character:', { name, description, traits });

            if (!name || !description) {
                alert('Please fill in name and description');
                return;
            }

            try {
                const response = await fetch('/characters', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        name: name,
                        description: description,
                        personality_traits: traits ? traits.split(',').map(t => t.trim()) : [],
                        nsfw_enabled: true
                    })
                });

                console.log('Response status:', response.status);

                if (response.ok) {
                    const result = await response.json();
                    console.log('Character created successfully:', result);
                    hideCreateCharacter();
                    loadCharacters();
                } else {
                    const errorText = await response.text();
                    console.error('Error response:', errorText);
                    alert('Error creating character: ' + errorText);
                }
            } catch (error) {
                console.error('Error creating character:', error);
                alert('Error creating character: ' + error.message);
            }
        }

        function toggleVoiceInput() {
            // Voice input functionality would be implemented here
            alert('Voice input feature coming soon!');
        }

        function playAudio(audioPath) {
            const audio = new Audio(`/media/${audioPath.split('/').pop()}`);
            audio.play().catch(e => console.log('Audio playback failed:', e));
        }
    </script>
</body>
</html>

"""
Main FastAPI application for AI Character Chat System
Provides REST API and WebSocket endpoints for all functionality
"""
import asyncio
import logging
import uvicorn
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import json

from fastapi import FastAP<PERSON>, HTTPException, WebSocket, WebSocketDisconnect, UploadFile, File
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Import our systems
from config import config
from character_system import character_manager, Character
from ollama_client import OllamaClient
from memory_system import MemoryManager
from speech_to_text import stt_system
from text_to_speech import tts_system
from image_generation import image_generator
from system_monitor import resource_monitor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(
    title="AI Character Chat System",
    description="Uncensored AI character chat with multimedia support",
    version="1.0.0"
)

# CORS middleware for web interface
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for API
class CharacterCreate(BaseModel):
    name: str
    description: str
    personality_traits: List[str] = []
    speaking_style: str = "casual"
    interests: List[str] = []
    background: str = ""
    voice_settings: Dict[str, Any] = {}
    image_style: str = "realistic_portrait"
    nsfw_enabled: bool = True
    temperature: float = 0.8

class MessageRequest(BaseModel):
    message: str
    conversation_id: str
    voice_response: bool = False

class ImageGenerationRequest(BaseModel):
    prompt: str
    style: str = "realistic_portrait"
    character_id: Optional[str] = None

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
    
    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
    
    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)
    
    async def broadcast(self, message: str):
        for connection in self.active_connections:
            await connection.send_text(message)

manager = ConnectionManager()

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize all systems on startup"""
    try:
        logger.info("Starting AI Character Chat System...")
        
        # Start resource monitoring
        asyncio.create_task(resource_monitor.start_monitoring())
        
        # Initialize Ollama client
        ollama_client = OllamaClient()
        await ollama_client.initialize()
        
        # Initialize memory system
        memory_manager = MemoryManager()
        await memory_manager.initialize()
        
        # Initialize STT system
        await stt_system.initialize()
        
        # Initialize TTS system (if available)
        try:
            from text_to_speech import TTS_AVAILABLE
            if TTS_AVAILABLE:
                await tts_system.initialize()
            else:
                logger.info("TTS system disabled - dependencies not available")
        except Exception as tts_error:
            logger.warning(f"TTS initialization failed: {tts_error}")
            logger.info("Continuing without TTS functionality")
        
        # Initialize image generation
        await image_generator.initialize()
        
        logger.info("All systems initialized successfully!")
        
    except Exception as e:
        logger.error(f"Startup failed: {e}")
        raise

# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    """Clean up resources on shutdown"""
    try:
        resource_monitor.stop_monitoring()
        stt_system.cleanup()
        tts_system.cleanup()
        image_generator.cleanup()
        logger.info("Cleanup completed")
    except Exception as e:
        logger.error(f"Shutdown error: {e}")

# API Routes

@app.get("/api/status")
async def api_status():
    """API status endpoint"""
    return {
        "message": "AI Character Chat System",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/system/status")
async def system_status():
    """Get system resource status"""
    return resource_monitor.get_system_status()

# Character management endpoints
@app.post("/characters", response_model=Character)
async def create_character(character_data: CharacterCreate):
    """Create a new AI character"""
    try:
        character = character_manager.create_character(character_data.dict())
        return character
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/characters", response_model=List[Character])
async def list_characters():
    """Get all characters"""
    return character_manager.list_characters()

@app.get("/characters/{character_id}", response_model=Character)
async def get_character(character_id: str):
    """Get specific character"""
    character = character_manager.get_character(character_id)
    if not character:
        raise HTTPException(status_code=404, detail="Character not found")
    return character

@app.put("/characters/{character_id}", response_model=Character)
async def update_character(character_id: str, updates: Dict[str, Any]):
    """Update character settings"""
    character = character_manager.update_character(character_id, updates)
    if not character:
        raise HTTPException(status_code=404, detail="Character not found")
    return character

@app.delete("/characters/{character_id}")
async def delete_character(character_id: str):
    """Delete a character"""
    success = character_manager.delete_character(character_id)
    if not success:
        raise HTTPException(status_code=404, detail="Character not found")
    return {"message": "Character deleted successfully"}

# Conversation endpoints
@app.post("/conversations/start")
async def start_conversation(character_id: str, user_id: str = "default_user"):
    """Start a new conversation"""
    try:
        conversation_id = await character_manager.start_conversation(character_id, user_id)
        return {"conversation_id": conversation_id}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

@app.post("/conversations/message")
async def send_message(request: MessageRequest):
    """Send message to character"""
    try:
        response = await character_manager.send_message(
            request.conversation_id, 
            request.message
        )
        
        # Generate voice response if requested
        if request.voice_response:
            voice_path = await tts_system.synthesize_speech(
                response["response"],
                voice_profile="female_1"  # Default voice
            )
            response["voice_file"] = voice_path
        
        return response
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

# Media generation endpoints
@app.post("/generate/image")
async def generate_image(request: ImageGenerationRequest):
    """Generate image from prompt"""
    try:
        image_path = await image_generator.generate_image(
            request.prompt,
            request.style
        )
        return {"image_path": image_path}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/generate/avatar/{character_id}")
async def generate_character_avatar(character_id: str, style: str = "realistic_portrait"):
    """Generate avatar for character"""
    character = character_manager.get_character(character_id)
    if not character:
        raise HTTPException(status_code=404, detail="Character not found")
    
    try:
        image_path = await image_generator.generate_character_avatar(
            character.name,
            character.description,
            style
        )
        return {"image_path": image_path}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Voice endpoints
@app.post("/voice/synthesize")
async def synthesize_speech(text: str, voice_profile: str = "female_1"):
    """Convert text to speech"""
    try:
        audio_path = await tts_system.synthesize_speech(text, voice_profile)
        return {"audio_path": audio_path}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/voice/transcribe")
async def transcribe_audio(file: UploadFile = File(...)):
    """Transcribe uploaded audio file"""
    try:
        # Save uploaded file temporarily
        temp_path = config.MEDIA_DIR / f"temp_{datetime.now().timestamp()}_{file.filename}"
        with open(temp_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Transcribe
        text = await stt_system.transcribe_file(str(temp_path))
        
        # Clean up
        temp_path.unlink(missing_ok=True)
        
        return {"text": text}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/voice/profiles")
async def get_voice_profiles():
    """Get available voice profiles"""
    return tts_system.get_available_voices()

# File serving
@app.get("/media/{filename}")
async def serve_media(filename: str):
    """Serve generated media files"""
    file_path = config.MEDIA_DIR / filename
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="File not found")
    return FileResponse(file_path)

# Serve static files and main page
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/", response_class=HTMLResponse)
async def serve_main_page():
    """Serve the main web interface"""
    try:
        with open("static/index.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        return HTMLResponse(content="<h1>Web interface not found</h1><p>Please ensure static/index.html exists</p>")

# WebSocket endpoint for real-time chat
@app.websocket("/ws/{character_id}")
async def websocket_endpoint(websocket: WebSocket, character_id: str):
    """WebSocket endpoint for real-time character chat"""
    await manager.connect(websocket)
    
    # Start conversation
    try:
        conversation_id = await character_manager.start_conversation(character_id)
        await websocket.send_text(json.dumps({
            "type": "connection_established",
            "conversation_id": conversation_id
        }))
    except Exception as e:
        await websocket.send_text(json.dumps({
            "type": "error",
            "message": str(e)
        }))
        return
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            if message_data["type"] == "message":
                # Send message to character
                response = await character_manager.send_message(
                    conversation_id,
                    message_data["content"]
                )
                
                # Send response back
                await websocket.send_text(json.dumps({
                    "type": "response",
                    "content": response["response"],
                    "character_name": response["character_name"],
                    "timestamp": response["timestamp"]
                }))
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)
        logger.info(f"WebSocket disconnected for character {character_id}")

if __name__ == "__main__":
    # Create media directories
    config.MEDIA_DIR.mkdir(exist_ok=True)
    
    # Run the application
    uvicorn.run(
        "main:app",
        host=config.API_HOST,
        port=config.API_PORT,
        reload=True,
        log_level="info"
    )
